package sitereport

import (
	basicpriceDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
	dailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
	statutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/statutory"
)

type SiteReportUseCase struct {
	sitereport          sitereportDmn.SiteReportDomainItf
	basicPrice          basicpriceDmn.BasicPriceDomainItf
	statutory           statutoryDmn.StatutoryDomainItf
	dailyReportAddition dailyreportadditionDmn.DailyReportAdditionDomainItf
}

type Domains struct {
	SiteReportDomain          sitereportDmn.SiteReportDomainItf
	BasicPriceDomain          basicpriceDmn.BasicPriceDomainItf
	StatutoryDomain           statutoryDmn.StatutoryDomainItf
	DailyReportAdditionDomain dailyreportadditionDmn.DailyReportAdditionDomainItf
}

func InitSiteReportUseCase(d Domains) *SiteReportUseCase {
	uc := &SiteReportUseCase{
		sitereport:          d.SiteReportDomain,
		basicPrice:          d.BasicPriceDomain,
		statutory:           d.StatutoryDomain,
		dailyReportAddition: d.DailyReportAdditionDomain,
	}
	return uc
}
