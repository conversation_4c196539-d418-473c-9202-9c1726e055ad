package site_report

import (
	"net/http"
	"time"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	"github.com/Sera-Global/be-nbs-accounting-system/server"
	sitereport "github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
	"github.com/labstack/echo/v4"
)

func GetList(c echo.Context) error {
	var status int

	var req sitereport.GetListReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	startDate, err := time.Parse(time.DateOnly, req.StartDate)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: ErrInvalidDateFormat.Error()})
	}
	req.ParsedStartDate = startDate

	endDate, err := time.Parse(time.DateOnly, req.EndDate)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: ErrInvalidDateFormat.Error()})
	}
	req.ParsedEndDate = endDate

	err = validateGetList(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.SiteReportUseCase.GetList(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.GetList]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func BulkUpdate(c echo.Context) error {
	var status int

	var req sitereport.BulkUpdateReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Get user roles from JWT context
	userRoles, ok := c.Get("roles").([]string)
	if !ok {
		status = http.StatusUnauthorized
		return c.JSON(status, types.BasicResp{Message: constanta.Unauthorized})
	}

	req.UserRoles = userRoles
	err = validateBulkUpdate(req)
	if err != nil {
		if err.Error() == constanta.Unauthorized {
			status = http.StatusUnauthorized
			return c.JSON(status, types.BasicResp{Message: err.Error()})
		}
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	err = server.SiteReportUseCase.BulkUpdate(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.BulkUpdate]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func GetStatutoryCalculationVariable(c echo.Context) error {
	var status int

	var req sitereport.GetStatutoryCalculationVariableReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Parse and validate time parameters
	err = parseAndValidateCalculationVariableRequest(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Validate request parameters
	err = validateGetStatutoryCalculationVariable(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.SiteReportUseCase.GetStatutoryCalculationVariable(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.GetStatutoryCalculationVariable]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}
