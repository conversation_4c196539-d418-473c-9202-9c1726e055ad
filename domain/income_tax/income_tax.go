package incometax

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	IncomeTaxDomainItf interface {
		GetList(ctx context.Context, param GetListReq) ([]IncomeTax, error)
		GetByAmount(ctx context.Context, param GetByAmountParam) (IncomeTax, error)
	}

	IncomeTaxResourceItf interface {
		getList(ctx context.Context, param GetListReq) ([]IncomeTax, error)
		getByAmount(ctx context.Context, param GetByAmountParam) (IncomeTax, error)
	}
)

// GetList retrieves all income tax records ordered by more_than.
func (d *IncomeTaxDomain) GetList(ctx context.Context, param GetListReq) ([]IncomeTax, error) {
	incomeTaxes, err := d.resource.getList(ctx, param)
	if err != nil {
		return []IncomeTax{}, log.LogError(err, nil)
	}
	return incomeTaxes, nil
}

// GetByAmount retrieves income tax record by amount range.
func (d *IncomeTaxDomain) GetByAmount(ctx context.Context, param GetByAmountParam) (IncomeTax, error) {
	incomeTax, err := d.resource.getByAmount(ctx, param)
	if err != nil {
		return IncomeTax{}, log.LogError(err, nil)
	}
	return incomeTax, nil
}
