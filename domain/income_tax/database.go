package incometax

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getList fetches all income tax records ordered by more_than.
func (rsc IncomeTaxResource) getList(ctx context.Context, param GetListReq) ([]IncomeTax, error) {
	var incomeTaxes []IncomeTax

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("code ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records and order by more_than ascending
	err := db.Where("deleted_at IS NULL").
		Order("more_than ASC").
		Find(&incomeTaxes).Error

	if err != nil {
		return []IncomeTax{}, log.LogError(err, nil)
	}

	return incomeTaxes, nil
}

// getByAmount fetches income tax record by amount range.
func (rsc IncomeTaxResource) getByAmount(ctx context.Context, param GetByAmountParam) (IncomeTax, error) {
	var incomeTax IncomeTax

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("more_than < ? AND less_than > ?", param.Amount, param.Amount).
		Where("deleted_at IS NULL").
		First(&incomeTax).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return IncomeTax{}, nil
		}
		return IncomeTax{}, log.LogError(err, nil)
	}

	return incomeTax, nil
}
