package qualification

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	QualificationDomainItf interface {
		GetList(ctx context.Context, param GetListReq) ([]Qualification, error)
		GetByID(ctx context.Context, param GetByIDParam) (Qualification, error)
		GetByIDs(ctx context.Context, param GetByIDsParam) ([]Qualification, error)
	}

	QualificationResourceItf interface {
		getList(ctx context.Context, param GetListReq) ([]Qualification, error)
		getByID(ctx context.Context, param GetByIDParam) (Qualification, error)
		getByIDs(ctx context.Context, param GetByIDsParam) ([]Qualification, error)
	}
)

// GetList retrieves all qualifications ordered by title.
func (d *QualificationDomain) GetList(ctx context.Context, param GetListReq) ([]Qualification, error) {
	qualifications, err := d.resource.getList(ctx, param)
	if err != nil {
		return []Qualification{}, log.LogError(err, nil)
	}
	return qualifications, nil
}

// GetByID retrieves qualification by ID.
func (d *QualificationDomain) GetByID(ctx context.Context, param GetByIDParam) (Qualification, error) {
	qualification, err := d.resource.getByID(ctx, param)
	if err != nil {
		return Qualification{}, log.LogError(err, nil)
	}
	return qualification, nil
}

// GetByIDs retrieves qualifications by IDs.
func (d *QualificationDomain) GetByIDs(ctx context.Context, param GetByIDsParam) ([]Qualification, error) {
	qualifications, err := d.resource.getByIDs(ctx, param)
	if err != nil {
		return []Qualification{}, log.LogError(err, nil)
	}
	return qualifications, nil
}
