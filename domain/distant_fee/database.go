package distantfee

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getList fetches all distant fees ordered by title.
func (rsc DistantFeeResource) getList(ctx context.Context, param GetListReq) ([]DistantFee, error) {
	var distantFees []DistantFee

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("title ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records and order by title ascending
	err := db.Where("deleted_at IS NULL").
		Order("title ASC").
		Find(&distantFees).Error

	if err != nil {
		return []DistantFee{}, log.LogError(err, nil)
	}

	return distantFees, nil
}

// getByID fetches distant fee by ID.
func (rsc DistantFeeResource) getByID(ctx context.Context, param GetByIDParam) (DistantFee, error) {
	var distantFee DistantFee

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id = ?", param.ID).
		Where("deleted_at IS NULL").
		First(&distantFee).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return DistantFee{}, nil
		}
		return DistantFee{}, log.LogError(err, nil)
	}

	return distantFee, nil
}
