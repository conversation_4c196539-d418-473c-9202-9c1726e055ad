package distantfee

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getList fetches all distant fees ordered by title.
func (rsc DistantFeeResource) getList(ctx context.Context, param GetListReq) ([]DistantFee, error) {
	var distantFees []DistantFee

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("title ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records and order by title ascending
	err := db.Where("deleted_at IS NULL").
		Order("title ASC").
		Find(&distantFees).Error

	if err != nil {
		return []DistantFee{}, log.LogError(err, nil)
	}

	return distantFees, nil
}
