package distantfee

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	DistantFeeDomainItf interface {
		GetList(ctx context.Context, param GetListReq) ([]DistantFee, error)
		GetByID(ctx context.Context, param GetByIDParam) (DistantFee, error)
	}

	DistantFeeResourceItf interface {
		getList(ctx context.Context, param GetListReq) ([]DistantFee, error)
		getByID(ctx context.Context, param GetByIDParam) (DistantFee, error)
	}
)

// GetList retrieves all distant fees ordered by title.
func (d *DistantFeeDomain) GetList(ctx context.Context, param GetListReq) ([]DistantFee, error) {
	distantFees, err := d.resource.getList(ctx, param)
	if err != nil {
		return []DistantFee{}, log.LogError(err, nil)
	}
	return distantFees, nil
}

// GetByID retrieves distant fee by ID.
func (d *DistantFeeDomain) GetByID(ctx context.Context, param GetByIDParam) (DistantFee, error) {
	distantFee, err := d.resource.getByID(ctx, param)
	if err != nil {
		return DistantFee{}, log.LogError(err, nil)
	}
	return distantFee, nil
}
